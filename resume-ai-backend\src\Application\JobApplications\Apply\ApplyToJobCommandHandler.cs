using Application.Abstractions.Authentication;
using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Domain.JobApplications;
using Domain.Jobs;
using Domain.Resumes;
using Microsoft.EntityFrameworkCore;
using SharedKernel;

namespace Application.JobApplications.Apply;

internal sealed class ApplyToJobCommandHandler(
    IApplicationDbContext context,
    IUserContext userContext)
    : ICommandHandler<ApplyToJobCommand, Guid>
{
    public async Task<Result<Guid>> Handle(ApplyToJobCommand command, CancellationToken cancellationToken)
    {
        // Check if the job exists
        Job? job = await context.Jobs
            .AsNoTracking()
            .FirstOrDefaultAsync(j => j.Id == command.JobId && !j.IsDeleted, cancellationToken);

        if (job is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.JobNotFound(command.JobId));
        }

        // Get the user's parent resume (the one with ParentId == null)
        Resume? parentResume = await context.Resumes
            .AsNoTracking()
            .FirstOrDefaultAsync(r => r.UserId == userContext.UserId && 
                                     (r.ParentId == null || r.ParentId == Guid.Empty) && 
                                     !r.IsDeleted, cancellationToken);

        if (parentResume is null)
        {
            return Result.Failure<Guid>(JobApplicationErrors.ResumeNotFound(Guid.Empty));
        }

        // Check if the user has already applied to this job with any resume
        bool alreadyApplied = await context.JobApplications
            .AnyAsync(ja => ja.JobId == command.JobId && ja.CreatedBy == userContext.UserId, cancellationToken);

        if (alreadyApplied)
        {
            return Result.Failure<Guid>(JobApplicationErrors.AlreadyExists(parentResume.Id, command.JobId));
        }

        // Create the job application
        var jobApplication = JobApplication.Create(parentResume.Id, command.JobId, userContext.UserId);

        context.JobApplications.Add(jobApplication);
        await context.SaveChangesAsync(cancellationToken);

        return jobApplication.Id;
    }
}
