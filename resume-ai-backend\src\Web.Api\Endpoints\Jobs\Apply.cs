using Application.Abstractions.Messaging;
using Application.JobApplications.Apply;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class ApplyToJob : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("jobs/{jobId:guid}/apply", async (
            Guid jobId,
            ICommandHandler<ApplyToJobCommand, Guid> handler,
            CancellationToken cancellationToken) =>
        {
            var command = new ApplyToJobCommand(jobId);

            Result<Guid> result = await handler.Handle(command, cancellationToken);

            return result.Match(
                jobApplicationId => Results.Created($"/job-applications/{jobApplicationId}", jobApplicationId),
                CustomResults.Problem);
        })
        .WithTags(Tags.Jobs)
        .WithName("ApplyToJob")
        .Produces<Guid>(StatusCodes.Status201Created)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
