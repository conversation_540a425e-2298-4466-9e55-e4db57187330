using Application.Abstractions.Messaging;
using Application.Jobs.Update;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class UpdateJob : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("jobs/{id:guid}", async (
            Guid id,
            UpdateJobRequest request,
            ICommandHandler<UpdateJobCommand> handler,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateJobCommand(
                id,
                request.JobTitle,
                request.JobDescription,
                request.JobPostingUrl,
                request.CompanyUrl,
                request.AppliedAt);

            Result result = await handler.Handle(command, cancellationToken);

            return result.Match(
                () => Results.NoContent(),
                CustomResults.Problem);
        })
        .WithTags(Tags.Jobs)
        .WithName("UpdateJob")
        .Produces(StatusCodes.Status204NoContent)
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}

public sealed record UpdateJobRequest(
    string JobTitle,
    string JobDescription,
    string JobPostingUrl,
    string CompanyUrl,
    DateTime? AppliedAt);
