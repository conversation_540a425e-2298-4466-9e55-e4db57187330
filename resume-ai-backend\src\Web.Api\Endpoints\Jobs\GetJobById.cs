using Application.Abstractions.Messaging;
using Application.Jobs.GetById;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class GetJobById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("jobs/{id:guid}", async (
            Guid id,
            IQueryHandler<GetJobByIdQuery, JobResponse> handler,
            CancellationToken cancellationToken) =>
        {
            var query = new GetJobByIdQuery(id);

            Result<JobResponse> result = await handler.Handle(query, cancellationToken);

            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags(Tags.Jobs)
        .WithName("GetJobById")
        .Produces<JobResponse>()
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}
