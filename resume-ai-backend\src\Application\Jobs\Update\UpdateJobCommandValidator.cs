using FluentValidation;

namespace Application.Jobs.Update;

public class UpdateJobCommandValidator : AbstractValidator<UpdateJobCommand>
{
    public UpdateJobCommandValidator()
    {
        RuleFor(c => c.JobId)
            .NotEmpty()
            .WithMessage("Job ID is required");

        RuleFor(c => c.<PERSON>itle)
            .NotEmpty()
            .WithMessage("Job title is required")
            .MaximumLength(200)
            .WithMessage("Job title cannot exceed 200 characters");

        RuleFor(c => c.JobDescription)
            .NotEmpty()
            .WithMessage("Job description is required")
            .MaximumLength(5000)
            .WithMessage("Job description cannot exceed 5000 characters");

        RuleFor(c => c.JobPostingUrl)
            .NotEmpty()
            .WithMessage("Job posting URL is required")
            .Must(BeAValidUrl)
            .WithMessage("Job posting URL must be a valid URL")
            .MaximumLength(2000)
            .WithMessage("Job posting URL cannot exceed 2000 characters");

        RuleFor(c => c.CompanyUrl)
            .NotEmpty()
            .WithMessage("Company URL is required")
            .Must(BeAValidUrl)
            .WithMessage("Company URL must be a valid URL")
            .MaximumLength(2000)
            .WithMessage("Company URL cannot exceed 2000 characters");

        RuleFor(c => c.AppliedAt)
            .LessThanOrEqualTo(DateTime.UtcNow)
            .WithMessage("Applied date cannot be in the future")
            .When(c => c.AppliedAt.HasValue);
    }

    private static bool BeAValidUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
        {
            return false;
        }

        return Uri.TryCreate(url, UriKind.Absolute, out Uri? result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}
