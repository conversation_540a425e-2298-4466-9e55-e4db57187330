using Application.Abstractions.AI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.CircuitBreaker;
using Polly.Extensions.Http;
using SharedKernel;
using System.Net;

namespace Infrastructure.AI;

internal sealed class ResilientAIResumeCustomizationService : IAIResumeCustomizationService
{
    private readonly IAIResumeCustomizationService _innerService;
    private readonly IAsyncPolicy<Result<AICustomizationResponse>> _retryPolicy;
    private readonly IAsyncPolicy<Result<AICustomizationResponse>> _circuitBreakerPolicy;
    private readonly IAsyncPolicy<Result<AICustomizationResponse>> _combinedPolicy;
    private readonly ILogger<ResilientAIResumeCustomizationService> _logger;

    public ResilientAIResumeCustomizationService(
        IAIResumeCustomizationService innerService,
        IOptions<AIServiceOptions> options,
        ILogger<ResilientAIResumeCustomizationService> logger)
    {
        _innerService = innerService;
        _logger = logger;

        AIServiceOptions aiOptions = options.Value;

        // Retry policy for transient failures
        _retryPolicy = Policy
            .HandleResult<Result<AICustomizationResponse>>(result => ShouldRetry(result))
            .WaitAndRetryAsync(
                retryCount: aiOptions.MaxRetries,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(
                    Math.Pow(aiOptions.RetryDelaySeconds, retryAttempt)), // Exponential backoff
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("AI service retry {RetryCount}/{MaxRetries} after {Delay}ms. Reason: {Reason}",
                        retryCount, aiOptions.MaxRetries, timespan.TotalMilliseconds,
                        outcome.Result?.Error?.Description ?? "Unknown error");
                });

        // Circuit breaker policy
        _circuitBreakerPolicy = Policy
            .HandleResult<Result<AICustomizationResponse>>(result => ShouldBreakCircuit(result))
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 3,
                durationOfBreak: TimeSpan.FromMinutes(2),
                onBreak: (result, duration) =>
                {
                    _logger.LogError("AI service circuit breaker opened for {Duration}ms. Reason: {Reason}",
                        duration.TotalMilliseconds, result.Result?.Error?.Description ?? "Unknown error");
                },
                onReset: () =>
                {
                    _logger.LogInformation("AI service circuit breaker reset - service is healthy again");
                },
                onHalfOpen: () =>
                {
                    _logger.LogInformation("AI service circuit breaker half-open - testing service health");
                });

        // Combine policies: Circuit breaker wraps retry
        _combinedPolicy = Policy.WrapAsync(_circuitBreakerPolicy, _retryPolicy);
    }

    public async Task<Result<AICustomizationResponse>> CustomizeResumeAsync(
        AICustomizationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _combinedPolicy.ExecuteAsync(async () =>
            {
                return await _innerService.CustomizeResumeAsync(request, cancellationToken);
            });
        }
        catch (BrokenCircuitException ex)
        {
            _logger.LogError(ex, "AI service circuit breaker is open - service unavailable");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ServiceUnavailable());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in resilient AI service wrapper");
            return Result.Failure<AICustomizationResponse>(AIServiceErrors.ProcessingFailed(ex.Message));
        }
    }

    private static bool ShouldRetry(Result<AICustomizationResponse> result)
    {
        if (result.IsSuccess)
        {

            return false;
        }


        Error error = result.Error;
        
        // Retry on transient errors
        return error.Code switch
        {
            "AIService.ServiceUnavailable" => true,
            "AIService.Timeout" => true,
            "AIService.ProcessingFailed" => true,
            _ => false
        };
    }

    private static bool ShouldBreakCircuit(Result<AICustomizationResponse> result)
    {
        if (result.IsSuccess)
        {

            return false;
        }


        Error error = result.Error;
        
        // Break circuit on persistent service issues
        return error.Code switch
        {
            "AIService.ServiceUnavailable" => true,
            "AIService.Timeout" => true,
            _ => false
        };
    }
}
