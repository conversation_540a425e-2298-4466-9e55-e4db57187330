using Application.Abstractions.Messaging;
using Application.JobApplications.GetById;
using Application.JobApplications.UpdateStatus;
using Domain.JobApplications;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.JobApplications;

internal sealed class UpdateStatus : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("job-applications/{jobApplicationId:guid}/status", async (
            Guid jobApplicationId,
            UpdateJobApplicationStatusRequest request,
            ICommandHandler<UpdateJobApplicationStatusCommand> commandHandler,
            IQueryHandler<GetJobApplicationByIdQuery, JobApplicationResponse> queryHandler,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateJobApplicationStatusCommand(jobApplicationId, request.Status);

            Result updateResult = await commandHandler.Handle(command, cancellationToken);

            if (updateResult.IsFailure)
            {
                return CustomResults.Problem(updateResult);
            }

            // Get the updated job application to return
            var query = new GetJobApplicationByIdQuery(jobApplicationId);
            Result<JobApplicationResponse> queryResult = await queryHandler.Handle(query, cancellationToken);

            return queryResult.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags(Tags.JobApplications)
        .WithName("UpdateJobApplicationStatus")
        .Produces<JobApplicationResponse>()
        .ProducesValidationProblem()
        .RequireAuthorization();
    }
}

public sealed record UpdateJobApplicationStatusRequest(JobApplicationStatus Status);
