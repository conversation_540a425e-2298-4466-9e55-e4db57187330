{"version": 2, "dgSpecHash": "31+jL3y/QtE=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\All\\Projects\\resume-ai\\resume-ai-backend\\src\\Aspire.AppHost\\Aspire.AppHost.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.3.1\\aspire.dashboard.sdk.win-x64.9.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting\\9.3.1\\aspire.hosting.9.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.apphost\\9.3.1\\aspire.hosting.apphost.9.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.3.1\\aspire.hosting.orchestration.win-x64.9.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.postgresql\\9.3.1\\aspire.hosting.postgresql.9.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.npgsql\\9.0.0\\aspnetcore.healthchecks.npgsql.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aspnetcore.healthchecks.uris\\9.0.0\\aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fractions\\7.3.0\\fractions.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.30.2\\google.protobuf.3.30.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore\\2.71.0\\grpc.aspnetcore.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore.server\\2.71.0\\grpc.aspnetcore.server.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.aspnetcore.server.clientfactory\\2.71.0\\grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.71.0\\grpc.core.api.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.client\\2.71.0\\grpc.net.client.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.clientfactory\\2.71.0\\grpc.net.clientfactory.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.common\\2.71.0\\grpc.net.common.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.tools\\2.72.0\\grpc.tools.2.72.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\json.more.net\\2.1.0\\json.more.net.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpatch.net\\3.3.0\\jsonpatch.net.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpointer.net\\5.2.0\\jsonpointer.net.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\kubernetesclient\\16.0.7\\kubernetesclient.16.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.5.192\\messagepack.2.5.192.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.5.192\\messagepack.annotations.2.5.192.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.4\\microsoft.extensions.configuration.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.4\\microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.4\\microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.4\\microsoft.extensions.configuration.commandline.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.4\\microsoft.extensions.configuration.environmentvariables.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.4\\microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.4\\microsoft.extensions.configuration.json.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\9.0.4\\microsoft.extensions.configuration.usersecrets.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.4\\microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.4\\microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.4\\microsoft.extensions.diagnostics.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.4\\microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks\\9.0.4\\microsoft.extensions.diagnostics.healthchecks.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.healthchecks.abstractions\\9.0.4\\microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.4\\microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.4\\microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.4\\microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\9.0.4\\microsoft.extensions.hosting.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.4\\microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\9.0.4\\microsoft.extensions.http.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.4\\microsoft.extensions.logging.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.4\\microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.4\\microsoft.extensions.logging.configuration.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.4\\microsoft.extensions.logging.console.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.4\\microsoft.extensions.logging.debug.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\9.0.4\\microsoft.extensions.logging.eventlog.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\9.0.4\\microsoft.extensions.logging.eventsource.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.4\\microsoft.extensions.options.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.4\\microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.4\\microsoft.extensions.primitives.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.6.3\\microsoft.net.stringtools.17.6.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading.only\\17.13.61\\microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.8.8\\microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nerdbank.streams\\2.11.90\\nerdbank.streams.2.11.90.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\8.0.3\\npgsql.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.5.2\\polly.core.8.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sonaranalyzer.csharp\\10.12.0.118525\\sonaranalyzer.csharp.10.12.0.118525.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\streamjsonrpc\\2.21.69\\streamjsonrpc.2.21.69.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.4\\system.diagnostics.eventlog.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\9.0.4\\system.io.hashing.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.1.0\\system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\16.3.0\\yamldotnet.16.3.0.nupkg.sha512"], "logs": []}